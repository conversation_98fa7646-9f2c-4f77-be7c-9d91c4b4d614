package com.knet.order.mq.producer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

import static com.knet.common.constants.OrderServicesConstants.*;

/**
 * <AUTHOR>
 * @date 2025/3/19 13:31
 * @description: 订单生产者
 */
@Slf4j
@Component
public class OrderProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送订单创建成功事件
     *
     * @param messageBody 消息体
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendOrderCreateEvent(String messageBody) {
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        // 构建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", ORDER_CREATED);
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "order-exchange",
                "order.created",
                message,
                correlationData
        );
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("order.created 消息到达Broker: {}", messageId);
                    } else {
                        log.error("order.created 消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("order.created 消息发送异常: {}", ex.getMessage());
                }
        );
    }

    /**
     * 发送订单创建失败事件
     *
     * @param messageBody 消息体
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendOrderCreateFailedEvent(String messageBody) {
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        // 构建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "order.failed");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "order-exchange",
                "order.failed",
                message,
                correlationData
        );
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("order.failed 消息到达Broker: {}", messageId);
                    } else {
                        log.error("order.failed 消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("order.failed 消息发送异常: {}", ex.getMessage());
                }
        );
    }

    /**
     * 发送延迟信息队列，用于创建订单后30分钟未支付，自动取消订单
     *
     * @param messageBody me
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendDelayedMessage(String messageBody) {
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "timeout.order");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        message.getMessageProperties().setHeader("x-delay", ORDER_TIMEOUT_TIME);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "order.delayed.exchange",
                "timeout.order",
                message,
                correlationData
        );
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("timeout.order 消息到达Broker: {}", messageId);
                    } else {
                        log.error("timeout.order 消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("timeout.order 消息发送异常: {}", ex.getMessage());
                }
        );
    }
}
